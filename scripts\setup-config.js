#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const configExamplePath = path.join(__dirname, '../electron/config.example.ts');
const configPath = path.join(__dirname, '../electron/config.ts');

// 检查配置文件是否存在
if (fs.existsSync(configPath)) {
  console.log('✅ 配置文件 electron/config.ts 已存在');
  process.exit(0);
}

// 检查模板文件是否存在
if (!fs.existsSync(configExamplePath)) {
  console.error('❌ 配置模板文件 electron/config.example.ts 不存在');
  process.exit(1);
}

try {
  // 复制模板文件
  fs.copyFileSync(configExamplePath, configPath);
  console.log('✅ 已创建配置文件 electron/config.ts');
  console.log('📝 请编辑该文件并填入你的实际 API 配置');
  console.log('');
  console.log('需要配置的项目：');
  console.log('  - baseURL: 你的 API 端点地址');
  console.log('  - apiKey: 你的 API 密钥');
  console.log('  - model: 你要使用的模型名称');
} catch (error) {
  console.error('❌ 创建配置文件失败:', error.message);
  process.exit(1);
}

#App {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(#deeefc, #f7f9fc);
}

.logo-box {
  position: relative;
  height: 9em;
}

.logo {
  position: absolute;
  left: calc(50% - 4.5em);
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .logo.electron {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-bar {
  app-region: drag;
  user-select: none;
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 42px;
  .title-bar-controls {
    height: 36px;
    .title-bar-controls__back {
    }

    .title-bar-controls__maximize {
    }

    .title-bar-controls__restore {
    }

    .title-bar-controls__close {
    }
  }
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  height: calc(100vh - 64px);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  
  > * {
    width: 100%;
  }
}

.ant-table-wrapper {
  flex: 1;
  overflow: hidden;
  
  .ant-table-container {
    height: 100%;
  }
  
  .ant-table-body {
    overflow-y: auto !important;
    height: calc(100vh - 200px) !important;
  }
}

.import-export {
  margin-bottom: 16px;
  flex-shrink: 0;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

# 翻译基准为英文功能说明

## 功能概述

在多语言翻译工具中新增了"翻译基准为英文"功能，允许用户切换翻译的基准语言，从默认的中文基准切换为英文基准。

## 功能特性

### 1. 新增"翻译基准为英文"开关
- 在主界面一键翻译按钮右侧新增了一个 Switch 开关
- 开关名称：翻译基准为英文
- 默认状态：关闭（使用中文作为基准）

### 2. 翻译逻辑变化
- **开关关闭（默认）**：以中文为基准，翻译成其他语言
- **开关开启**：以英文为基准，翻译成其他语言（包括中文）

### 3. 翻译队列筛选条件变化
- **开关关闭**：筛选有中文但缺少其他语言的行
- **开关开启**：筛选有英文但缺少其他语言的行

### 4. 提示词自动调整
- 根据基准语言自动调整翻译提示词
- 英文基准时使用专门的英文翻译提示词

## 使用方法

### 步骤 1：切换翻译基准
1. 在主界面找到"翻译基准为英文"开关
2. 点击开关切换基准语言
   - 关闭：使用中文作为翻译基准
   - 开启：使用英文作为翻译基准

### 步骤 2：执行翻译
1. 点击"一键翻译"按钮
2. 在弹出的对话框中，系统会根据基准语言筛选需要翻译的行
3. 设置翻译最大长度（可选）
4. 选择要翻译的行并开始翻译

## 功能场景

### 场景 1：中文基准翻译（默认）
- **数据要求**：行中有中文内容
- **翻译方向**：中文 → 英文、日文、韩文等
- **筛选条件**：有中文但缺少其他语言的行

### 场景 2：英文基准翻译
- **数据要求**：行中有英文内容
- **翻译方向**：英文 → 中文、日文、韩文等
- **筛选条件**：有英文但缺少其他语言的行

## 技术实现

### 1. 组件修改

#### ImportExport.tsx
```typescript
interface ImportExportProps {
  // ... 其他属性
  useEnglishAsBase: boolean;
  onUseEnglishAsBaseChange: (checked: boolean) => void;
}
```

#### TranslationSelector.tsx
```typescript
interface TranslationSelectorProps {
  // ... 其他属性
  useEnglishAsBase?: boolean;
}
```

### 2. 状态管理
```typescript
const [useEnglishAsBase, setUseEnglishAsBase] = useState(false);
```

### 3. 翻译逻辑调整
```typescript
// 根据基准语言确定源语言键
const sourceKey = useEnglishAsBase 
  ? 'values' // 英文作为基准
  : (Object.keys(languageConfig).find(key =>
      languageConfig[key].label.includes('中文') || key === 'values-zh-rCN'
    ) || 'values-zh-rCN'); // 中文作为基准
```

### 4. 提示词系统
```typescript
// 基于英文的特殊语言提示词
const englishBasedPrompts: Record<string, string> = {
  'English': '原样保留文本，不要做任何修改：',
  '繁体中文': '将下面的英文翻译成繁体中文，只返回翻译结果，不要加任何解释。',
  '中文': '将下面的英文翻译成简体中文，只返回翻译结果，不要加任何解释。'
};
```

## 界面变化

### 主界面布局
```
[导入多语言表格] [一键翻译] [🔄 翻译基准为英文] [导出]
```

### 选择翻译行对话框
- 表格列标题根据基准语言动态变化
- 中文基准：显示"中文"列
- 英文基准：显示"英文"列

## 注意事项

1. **数据要求**：
   - 中文基准：Excel 中必须有中文列数据
   - 英文基准：Excel 中必须有英文列数据

2. **兼容性**：
   - 现有功能完全兼容
   - 切换基准语言会自动更新翻译提示词

3. **翻译质量**：
   - 不同基准语言可能产生不同的翻译结果
   - 建议根据实际需求选择合适的基准语言

4. **性能影响**：
   - 切换基准语言时会重新初始化翻译提示词
   - 不影响翻译速度和并发处理

## 测试建议

1. 测试中文基准翻译功能
2. 测试英文基准翻译功能
3. 测试基准语言切换的实时性
4. 验证不同基准语言下的翻译质量
5. 测试与翻译最大长度功能的兼容性

interface ElectronAPI {
  translateText: (params: { 
    text: string; 
    targetLanguage: string; 
    prompt: string; 
  }) => Promise<string>;
}

interface IpcRenderer {
  send(channel: string, ...args: any[]): void;
  on(channel: string, func: (...args: any[]) => void): void;
  off(channel: string, func: (...args: any[]) => void): void;
  once(channel: string, func: (...args: any[]) => void): void;
  invoke(channel: string, ...args: any[]): Promise<any>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    ipcRenderer: Ipc<PERSON><PERSON><PERSON>;
  }
}

export {}; 
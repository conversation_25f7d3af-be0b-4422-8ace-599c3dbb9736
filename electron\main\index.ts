import { app, B<PERSON>erWindow, shell, ipc<PERSON>ain, dialog } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import os from 'node:os'
import { update } from './update'
import fs from 'fs/promises'
import axios from 'axios'
import { join } from 'path'
import { API_CONFIG } from '../config'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main
// │ │ └── index.js    > Electron-Main
// │ └─┬ preload
// │   └── index.mjs   > Preload-Scripts
// ├─┬ dist
// │ └── index.html    > Electron-Renderer
//
process.env.APP_ROOT = path.join(__dirname, '../..')

export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')
export const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST

// Disable GPU Acceleration for Windows 7
if (os.release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

let win: BrowserWindow | null = null
const preload = path.join(__dirname, '../preload/index.js')
const indexHtml = path.join(RENDERER_DIST, 'index.html')

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${API_CONFIG.apiKey}`
  }
});

async function createWindow() {
  win = new BrowserWindow({
    title: '多语言管理系统',
    icon: path.join(process.env.VITE_PUBLIC, 'favicon.ico'),
    width: 1280,
    height: 720,
    minWidth: 1024,
    minHeight: 700,
    frame: false,
    webPreferences: {
      preload,
      contextIsolation: true,
      nodeIntegration: false,
      sandbox: false,
    },
  })

  if (VITE_DEV_SERVER_URL) { // #298
    win.loadURL(VITE_DEV_SERVER_URL)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    win.loadFile(indexHtml)
  }

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })

  // Auto update
  update(win)

  // 确保 win 存在后再添加事件监听
  win.on('maximize', () => {
    if (win) {
      win.webContents.send('window-maximized-state', true);
    }
  });

  win.on('unmaximize', () => {
    if (win) {
      win.webContents.send('window-maximized-state', false);
    }
  });

  // 添加错误处理
  win.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Window load failed:', errorCode, errorDescription);
  });

  // 添加预加载脚本加载失败的处理
  win.webContents.on('preload-script-failed-load', (event, preloadPath) => {
    console.error('Preload script failed to load:', preloadPath);
  });
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  win = null
  if (process.platform !== 'darwin') app.quit()
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})

// New window example arg: new windows url
ipcMain.handle('open-win', (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: true,
    },
  })

  if (VITE_DEV_SERVER_URL) {
    childWindow.loadURL(VITE_DEV_SERVER_URL);
  } else {
    childWindow.loadFile(join(process.env.DIST as string, 'index.html'));
  }

  // Test actively push message to the Electron-Renderer
  childWindow.webContents.on('did-finish-load', () => {
    childWindow?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  childWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })

  // Auto update
  update(childWindow)

  // 确保 childWindow 存在后再添加事件监听
  childWindow.on('maximize', () => {
    if (childWindow) {
      childWindow.webContents.send('window-maximized-state', true);
    }
  });

  childWindow.on('unmaximize', () => {
    if (childWindow) {
      childWindow.webContents.send('window-maximized-state', false);
    }
  });
})

ipcMain.on('window-minimize', () => {
  const win = BrowserWindow.getFocusedWindow()
  win?.minimize()
})

ipcMain.on('window-maximize', () => {
  const win = BrowserWindow.getFocusedWindow()
  if (win?.isMaximized()) {
    win.unmaximize()
  } else {
    win?.maximize()
  }
})

ipcMain.on('window-close', () => {
  const win = BrowserWindow.getFocusedWindow()
  win?.close()
})

// 监听窗口最大化状态变化
function handleWindowMaximizeEvents(win: BrowserWindow) {
  win.on('maximize', () => {
    win.webContents.send('window-maximized-state', true)
  })

  win.on('unmaximize', () => {
    win.webContents.send('window-maximized-state', false)
  })
}

// 添加翻译处理程序
ipcMain.handle('translate-text', async (event, params) => {
  try {
    const { text, targetLanguage, prompt } = params;

    console.log('发送翻译请求:', {
      URL: `${API_CONFIG.baseURL}/api/v3/chat/completions`,
      原文: text,
      目标语言: targetLanguage,
      提示: prompt
    });

    // const response = await apiClient.post('/api/v3/chat/completions', {  // 火山
    const response = await apiClient.post('/v1/chat/completions', { // gpt
      model: API_CONFIG.model,
      messages: [
        {
          role: 'user',
          content: `${prompt}横线以后是待翻译文本\n--------------------------\n${text}`
        }
      ],
      temperature: 0.7,
      max_tokens: 800
    });

    console.log('API 响应:', {
      状态码: response.status,
      响应头: response.headers,
      响应数据: response.data
    });

    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('无效的翻译响应');
    }

    return response.data.choices[0].message.content;

  } catch (error) {
    console.error('翻译请求错误:', error);

    if (axios.isAxiosError(error)) {
      console.error('API 错误详情:', {
        状态码: error.response?.status,
        错误信息: error.response?.data,
        请求URL: error.config?.url,
        请求方法: error.config?.method,
        请求头: error.config?.headers
      });

      // 根据不同的错误状态返回具体的错误信息
      switch (error.response?.status) {
        case 401:
          throw new Error('API 认证失败，请检查 API 密钥');
        case 404:
          throw new Error('API 端点不存在，请检查 API 地址');
        case 429:
          throw new Error('API 调用频率超限，请稍后再试');
        case 500:
          throw new Error('API 服务器错误，请稍后再试');
        default:
          throw new Error(`API 请求失败: ${error.message}`);
      }
    }

    throw error;
  }
});

// 处理保存对话框
ipcMain.handle('show-save-dialog', async (event, options) => {
  try {
    console.log('显示保存对话框，参数:', options);

    const result = await dialog.showSaveDialog({
      title: '保存Excel文件',
      defaultPath: options.defaultPath,
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] }
      ],
      properties: ['createDirectory', 'showOverwriteConfirmation']
    });

    console.log('对话框返回结果:', result);
    return {
      canceled: result.canceled,
      filePath: result.filePath || ''
    };
  } catch (error) {
    console.error('显示保存对话框失败:', error);
    return {
      canceled: true,
      filePath: '',
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
});

// 处理文件保存
ipcMain.handle('save-excel-file', async (event, params) => {
  try {
    console.log('主进程: 开始保存文件:', {
      文件路径: params.filePath,
      数据大小: params.buffer.length
    });

    const { buffer, filePath } = params;

    // 检查文件路径是否存在
    const directory = path.dirname(filePath);
    try {
      await fs.access(directory);
    } catch (error) {
      console.log('主进程: 创建目录:', directory);
      await fs.mkdir(directory, { recursive: true });
    }

    // 使用同步方法写入文件
    await fs.writeFile(filePath, Buffer.from(buffer));

    console.log('主进程: 文件保存成功');
    return { success: true, message: '文件保存成功' };
  } catch (error) {
    console.error('主进程: 保存文件失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
});
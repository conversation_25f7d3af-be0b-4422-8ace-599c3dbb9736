# 翻译最大长度功能说明

## 功能概述

在多语言翻译工具中新增了"翻译最大长度"功能，允许用户为每一行翻译内容设置最大长度限制。

## 功能特性

### 1. 新增"翻译最大长度"列
- 在"选择需要翻译的行"弹窗中，复选框列的右侧新增了"翻译最大长度"列
- 每行都有一个独立的数字输入框
- 默认值为 0（表示不限制长度）
- 输入范围：0-999

### 2. 长度限制提示词
- 当设置的最大长度大于 0 时，翻译请求会在提示词中添加长度限制说明
- 提示词格式：`你返回的翻译内容最大长度为{maxLength}，若需要压缩翻译内容，请尽量遵循原意去压缩`

## 使用方法

### 步骤 1：点击一键翻译
1. 在主界面点击"一键翻译"按钮
2. 弹出"选择需要翻译的行"对话框

### 步骤 2：设置翻译最大长度
1. 在对话框中，可以看到新增的"翻译最大长度"列
2. 为需要限制长度的行输入最大长度值（0-999）
3. 0 表示不限制长度，大于 0 的值表示具体的字符数限制

### 步骤 3：开始翻译
1. 选择需要翻译的行（复选框）
2. 点击"开始翻译"按钮
3. 系统会根据设置的最大长度进行翻译

## 技术实现

### 数据结构修改
```typescript
export interface LanguageData {
  key: string;
  module: string;
  values: {
    [key: string]: string;
  };
  maxLength?: number; // 新增：翻译最大长度
}
```

### 组件修改
1. **TranslationSelector.tsx**：
   - 添加 InputNumber 组件用于输入最大长度
   - 管理每行的最大长度状态
   - 在确认时将最大长度信息传递给父组件

2. **translationService.ts**：
   - 修改 `translateText` 函数，支持 `maxLength` 参数
   - 在提示词中添加长度限制说明

3. **App.tsx**：
   - 在调用翻译服务时传递最大长度参数

## 示例场景

### 场景 1：UI 按钮文本翻译
- 原文："确认提交"
- 设置最大长度：10
- 翻译时会提示 AI 将英文翻译控制在 10 个字符以内

### 场景 2：标题翻译
- 原文："用户管理系统设置页面"
- 设置最大长度：20
- 翻译时会提示 AI 将翻译内容压缩到 20 个字符以内

### 场景 3：无长度限制
- 原文："这是一段详细的说明文字..."
- 设置最大长度：0
- 正常翻译，不添加长度限制

## 注意事项

1. **长度计算**：最大长度限制是基于字符数，不是字节数
2. **AI 理解**：长度限制是通过提示词告知 AI，实际效果取决于 AI 模型的理解和执行能力
3. **压缩策略**：当需要压缩时，AI 会尽量保持原意的基础上进行压缩
4. **兼容性**：现有数据不受影响，maxLength 字段为可选字段

## 测试建议

1. 测试不同长度限制下的翻译效果
2. 验证长度为 0 时的正常翻译功能
3. 测试极端值（1、999）的处理情况
4. 验证批量翻译时每行独立的长度限制

{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "noImplicitAny": false, "noImplicitThis": false, "alwaysStrict": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "checkJs": false}, "include": ["src", "electron"], "references": [{"path": "./tsconfig.node.json"}]}
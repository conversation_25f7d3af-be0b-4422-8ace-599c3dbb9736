import React from 'react';
import { Modal, Progress } from 'antd';
import './TranslationProgress.scss';

interface TranslationProgressProps {
  visible: boolean;
  current: number;
  total: number;
  currentLanguage: string;
  currentText: string;
  queueStatus?: {
    activeRequests: number;
    queueLength: number;
  };
}

export const TranslationProgress: React.FC<TranslationProgressProps> = ({
  visible,
  current,
  total,
  currentLanguage,
  currentText,
  queueStatus
}) => {
  const percent = Math.floor((current / total) * 100);

  return (
    <Modal
      title="翻译进度"
      open={visible}
      footer={null}
      closable={false}
      maskClosable={false}
      centered
      width={500}
    >
      <div className="translation-progress">
        <Progress
          percent={percent}
          status="active"
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
        <div className="progress-info">
          <div className="translation-details">
            <div>正在翻译: {currentLanguage}</div>
            <div className="current-text">
              原文: {currentText}
            </div>
          </div>
          <div className="progress-count">
            进度: {current} / {total}
          </div>
          {queueStatus && (
            <div className="queue-status">
              <div>并发请求: {queueStatus.activeRequests}</div>
              <div>等待队列: {queueStatus.queueLength}</div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};
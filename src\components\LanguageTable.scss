.language-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  min-height: 0;

  .table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    
    .ant-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
    
    .ant-table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }

    .ant-table-body {
      flex: 1;
      overflow: auto !important;
      
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        background-color: #f5f5f5;
        display: block !important;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #bfbfbf !important;
        border-radius: 4px;
        height: calc(100% - 20px) !important;
        margin-top: 20px !important;
        
        &:hover {
          background: #a6a6a6 !important;
        }
      }
      
      &::-webkit-scrollbar-track {
        background: #f0f0f0;
        border-radius: 4px;
      }
    }
  }

  .table-footer {
    flex-shrink: 0;
    padding: 8px 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    z-index: 10;

    .ant-btn {
      height: 32px;
      font-size: 14px;
      width: 100%;
    }
  }
}

// 固定列样式优化
.ant-table-cell {
  &.ant-table-cell-fix-left,
  &.ant-table-cell-fix-right {
    z-index: 2;
  }
}

// 添加全局样式确保滚动条始终显示
::-webkit-scrollbar {
  display: block !important;
} 
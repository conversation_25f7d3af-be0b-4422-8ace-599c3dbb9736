/// <reference types="vite/client" />

interface IpcRenderer {
  send: (channel: string, ...args: any[]) => void;
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;
}

declare global {
  interface Window {
    ipcRenderer: IpcRenderer;
    electron: {
      showSaveDialog: (options: any) => Promise<any>;
      saveExcelFile: (params: any) => Promise<any>;
      translateText: (params: any) => Promise<any>;
      windowControl: {
        minimize: () => void;
        maximize: () => void;
        close: () => void;
      };
      on: (channel: string, callback: Function) => void;
      off: (channel: string, callback: Function) => void;
    };
  }
}

export {};

import React, { useEffect, useRef, useMemo, useState } from 'react';
import './LanguageTable.scss';
import { Table, Button, Tooltip, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { LanguageData } from '../types';
import { TranslationOutlined, PlusOutlined } from '@ant-design/icons';

interface LanguageTableProps {
  data: LanguageData[];
  languageConfig: Record<string, { key: string; label: string }>;
  onAddNewRow: () => void;
}

export const LanguageTable: React.FC<LanguageTableProps> = ({ 
  data, 
  languageConfig, 
  onAddNewRow 
}) => {
  const tableBodyRef = useRef<HTMLDivElement>(null);
  const [tableHeight, setTableHeight] = useState(500); // 默认高度

  // 计算表格高度
  useEffect(() => {
    const calculateTableHeight = () => {
      // 增加其他元素的高度估计值
      const otherElementsHeight = 300; // 从280减少到260，这样表格区域会增加20px
      const windowHeight = window.innerHeight;
      const newHeight = windowHeight - otherElementsHeight;
      setTableHeight(Math.max(400, newHeight)); // 设置最小高度为400px
    };

    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);

    return () => {
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, []);

  // 添加数据验证
  useEffect(() => {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('表格数据无效或为空:', {
        是否为数组: Array.isArray(data),
        数据量: data?.length
      });
    } else {
      console.log('表格数据有效:', {
        数据量: data.length,
        第一条数据: data[0]
      });
    }
  }, [data]);

  // 添加数据检查
  useEffect(() => {
    console.log('表格数据更新:', {
      数据量: data.length,
      示例数据: data[0]
    });
  }, [data]);

  // 使用 useMemo 计算列配置和总宽度
  const { columns, totalWidth } = useMemo(() => {
    const fixedColumns: ColumnsType<LanguageData> = [
      {
        title: '模块',
        dataIndex: 'module',
        key: 'module',
        width: 150,
        fixed: 'left',
        ellipsis: true,
      },
      {
        title: 'Key',
        dataIndex: 'key',
        key: 'key',
        width: 200,
        fixed: 'left',
        ellipsis: true,
      },
      {
        title: '中文',
        dataIndex: ['values', 'values-zh-rCN'],
        key: 'values-zh-rCN',
        width: 200,
        fixed: 'left',
        ellipsis: true,
        render: (text: string) => text || '-'
      }
    ];

    const scrollableColumns: ColumnsType<LanguageData> = Object.entries(languageConfig)
      .filter(([key]) => key !== 'values-zh-rCN')
      .map(([key, config]) => ({
        title: config.label,
        dataIndex: ['values', key],
        key: key,
        width: 200,
        ellipsis: true,
        render: (text: string) => text || '-'
      }));

    // 计算总宽度
    const fixedWidth = 650; // 固定列的总宽度
    const scrollableWidth = scrollableColumns.length * 200; // 可滚动列的总宽度
    const totalWidth = fixedWidth + scrollableWidth;

    return {
      columns: [...fixedColumns, ...scrollableColumns],
      totalWidth
    };
  }, [languageConfig]);

  // 监听数据变化，自动滚动到底部
  useEffect(() => {
    if (tableBodyRef.current && data.length > 0) {
      const scrollElement = tableBodyRef.current.querySelector('.ant-table-body');
      if (scrollElement) {
        setTimeout(() => {
          scrollElement.scrollTop = scrollElement.scrollHeight;
        }, 100);
      }
    }
  }, [data.length]);

  // 表格配置
  const tableProps = {
    virtual: true,
    scroll: { 
      x: totalWidth,
      y: tableHeight 
    },
    pagination: false as const,
    bordered: true,
    size: 'small' as const,
    sticky: true,
    tableLayout: 'fixed' as const,
  };

  return (
    <div className="language-table-container" ref={tableBodyRef}>
      <div className="table-wrapper">
        <Table
          columns={columns}
          dataSource={data}
          rowKey="key"
          {...tableProps}
        />
      </div>
      <div className="table-footer">
        <Button 
          type="dashed" 
          icon={<PlusOutlined />}
          onClick={onAddNewRow}
          size="middle"
          style={{ width: '100%' }}
        >
          添加新行
        </Button>
      </div>
    </div>
  );
}; 
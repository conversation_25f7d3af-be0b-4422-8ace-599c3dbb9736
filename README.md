# 看家王多语言翻译工具

![gif](/src/assets/Operation_Tutorial.gif)

### 安装依赖
```
npm install
```

### dev运行
```
npm run dev
```
- 第一次运行速度比较慢，要等到整个页面都显示出来。之后会比较快

### 构建应用程序
```
npm run build
```
- 构建后的文件在release目录下，可以直接运行
- 目录里面win-unpacked代表免安装可直接运行的程序
- .exe目录代表打包后的程序


## 配置说明

### API 配置
1. 复制 `electron/config.example.ts` 为 `electron/config.ts`
2. 在 `electron/config.ts` 中填入你的实际 API 配置：
   - `baseURL`: 你的 API 端点地址
   - `apiKey`: 你的 API 密钥
   - `model`: 你要使用的模型名称

```bash
# 复制配置文件模板
cp electron/config.example.ts electron/config.ts
```

然后编辑 `electron/config.ts` 文件，填入你的实际配置。

### Excel文件说明

1. Excel文件的表头必须包含"模块"和"Key"两列，其他列为需要翻译的目标语言
2. "模块"和"Key"两列必须在最前面，且"模块"列必须在"Key"列的前面
3. "模块"和"Key"两列的列名可以自定义，但是必须包含在表头中
4. "Key"列为每一行的唯一标识，不能重复
5. "模块"没有实际作用，只是用来分类
6. 翻译的目标语言列名可以自定义，但语言名必须排在前面。比如为"简体中文_values-zh-CN"
7. 表头图片参考：
![image](/src/assets/excel-example.png) 
8. 操作教程请参考最上面的gif图

### 功能特性
- ✅ 支持多语言翻译（基于 AI 大模型）
- ✅ Excel 文件导入导出
- ✅ 翻译最大长度限制
- ✅ 翻译基准语言切换（中文/英文）
- ✅ 批量翻译和单行翻译
- ✅ 翻译进度显示和队列管理
- ✅ 翻译结果导出

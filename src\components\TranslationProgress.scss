.translation-progress {
  padding: 20px 0;

  .progress-info {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .translation-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
      color: #666;
      font-size: 14px;

      .current-text {
        color: #333;
        background: #f5f5f5;
        padding: 8px;
        border-radius: 4px;
        word-break: break-all;
      }
    }

    .progress-count {
      color: #666;
      font-size: 14px;
    }

    .queue-status {
      margin-top: 8px;
      padding: 8px;
      background-color: #f0f8ff;
      border-radius: 4px;
      border-left: 3px solid #1890ff;

      div {
        color: #666;
        font-size: 14px;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
import axios from 'axios';
import { LanguageConfig } from '../types';

// 最大并发请求数
const MAX_CONCURRENT_REQUESTS = 100;

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

// 请求队列管理器
class RequestQueueManager {
  private queue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private maxConcurrent: number;

  constructor(maxConcurrent: number) {
    this.maxConcurrent = maxConcurrent;
  }

  // 添加请求到队列
  public enqueue<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // 将请求包装成一个任务
      const task = async () => {
        try {
          this.activeRequests++;
          const result = await requestFn();
          resolve(result);
          return result;
        } catch (error) {
          reject(error);
          throw error;
        } finally {
          this.activeRequests--;
          this.processQueue();
        }
      };

      // 将任务添加到队列
      this.queue.push(task);

      // 尝试处理队列
      this.processQueue();
    });
  }

  // 处理队列中的请求
  private processQueue() {
    // 如果当前活跃请求数小于最大并发数，且队列中有等待的请求
    while (this.activeRequests < this.maxConcurrent && this.queue.length > 0) {
      // 取出队列中的第一个请求并执行
      const nextTask = this.queue.shift();
      if (nextTask) {
        nextTask().catch(() => {
          // 错误已在任务内部处理
        });
      }
    }
  }

  // 获取当前队列状态
  public getStatus() {
    return {
      queueLength: this.queue.length,
      activeRequests: this.activeRequests
    };
  }
}

// 创建请求队列管理器实例
const requestQueue = new RequestQueueManager(MAX_CONCURRENT_REQUESTS);

// 导出获取队列状态的函数
export function getQueueStatus() {
  return requestQueue.getStatus();
}

// 默认语言提示
const languagePrompts: Record<string, string> = {
  'values': '将下面的文本翻译成英语，只返回翻译结果，不要加任何解释。\n\n',
  'values-ja': '将下面的文本翻译成日语，只返回翻译结果，不要加任何解释或者读音注释。\n\n',
  'values-ko': '将下面的文本翻译成韩语，只返回翻译结果，不要加任何解释或者读音注释。\n\n',
  'values-pt': '将下面的文本翻译成葡萄牙语，只返回翻译结果，不要加任何解释。\n\n',
  'values-de': '将下面的文本翻译成德语，只返回翻译结果，不要加任何解释。\n\n',
  'values-tr': '将下面的文本翻译成土耳其语，只返回翻译结果，不要加任何解释。\n\n',
  'values-nl': '将下面的文本翻译成荷兰语，只返回翻译结果，不要加任何解释。\n\n',
  'values-es': '将下面的文本翻译成西班牙语，只返回翻译结果，不要加任何解释。\n\n',
  'values-ru': '将下面的文本翻译成俄语，只返回翻译结果，不要加任何解释。\n\n',
  'values-zh-rHK': '将下面的简体中文翻译成繁体中文，只返回翻译结果，不要加任何解释。\n\n',
  'values-th': '将下面的文本翻译成泰语，只返回翻译结果，不要加任何解释。\n\n',
  'values-vi': '将下面的文本翻译成越南语，只返回翻译结果，不要加任何解释。\n\n',
  'values-ar': '将下面的文本翻译成阿拉伯语，只返回翻译结果，不要加任何解释。\n\n',
  'values-fr': '将下面的文本翻译成法语，只返回翻译结果，不要加任何解释。\n\n',
  'values-it': '将下面的文本翻译成意大利语，只返回翻译结果，不要加任何解释。\n\n'
};

// 特殊语言处理
const specialLanguagePrompts: Record<string, string> = {
  'English': '将下面的文本翻译成英语，只返回翻译结果，不要加任何解释。\n\n',
  '繁体中文': '将下面的文本翻译成繁体中文，只返回翻译结果，不要加任何解释。\n\n',
};

// 基于英文的特殊语言提示词
const englishBasedPrompts: Record<string, string> = {
  'English': '原样保留文本，不要做任何修改：',
  '繁体中文': '将下面的英文翻译成繁体中文，只返回翻译结果，不要加任何解释。\n\n',
  '中文': '将下面的英文翻译成简体中文，只返回翻译结果，不要加任何解释。\n\n'
};

// 更新语言提示
export function updateLanguagePrompts(config: Record<string, LanguageConfig>, useEnglishAsBase: boolean = false): void {
  // 清除现有的提示
  Object.keys(languagePrompts).forEach(key => {
    delete languagePrompts[key];
  });

  // 添加新的提示
  Object.entries(config).forEach(([langKey, langConfig]) => {
    const label = langConfig.label;

    let prompt: string;
    if (useEnglishAsBase) {
      // 基于英文翻译
      prompt = englishBasedPrompts[label] ||
               `将下面的英文翻译成${label}，只返回翻译结果，不要加任何解释。\n\n`;
    } else {
      // 基于中文翻译（原有逻辑）
      prompt = specialLanguagePrompts[label] ||
               `将下面的文本翻译成${label}，只返回翻译结果，不要加任何解释。\n\n`;
    }

    languagePrompts[langKey] = prompt;

    // 调试输出
    console.log(`为语言 ${label} (键: ${langKey}) 设置翻译提示: ${prompt}`);
  });

  console.log('已更新翻译提示，总数:', Object.keys(languagePrompts).length);
  console.log('所有语言键:', Object.keys(languagePrompts));
}

export async function translateText(text: string, targetLanguage: string, maxLength?: number, sourceLanguage?: string, field?: string): Promise<string> {
  // 使用请求队列管理器来控制并发
  return requestQueue.enqueue(async () => {
    try {
      // 获取队列状态
      const queueStatus = requestQueue.getStatus();
      console.log('翻译请求队列状态:', {
        当前活跃请求数: queueStatus.activeRequests,
        等待队列长度: queueStatus.queueLength,
        最大并发数: MAX_CONCURRENT_REQUESTS
      });

      console.log('准备翻译请求:', {
        原文: text,
        目标语言: targetLanguage,
        提示语: languagePrompts[targetLanguage]
      });

      let prompt = languagePrompts[targetLanguage] || '请将以下中文文本翻译成对应语言：';

      const fieldValue = field || "通用"

      // 如果设置了最大长度且大于0，则在提示词中添加长度限制
      if (maxLength && maxLength > 0) {
        prompt = `##角色：你是一个专业${fieldValue}领域翻译工具。你不仅需要翻译，还要尽量在不改变原文本原意的情况下最大化压缩文本。##要求:若源文没有带回车字符,你也不能输出回车字符。若源文包含%s或者回车字符，你也要输出同样的字符。你返回的翻译内容最大长度为${maxLength}。 ##任务:` + prompt;
      }else {
        prompt = `##角色：你是一个专业${fieldValue}领域翻译工具。你不仅需要翻译，还要尽量在不改变原文本原意的情况下最大化压缩文本。##要求:若源文没有带回车字符,你也不能输出回车字符。若源文包含%s或者回车字符，你也要输出同样的字符。 ##任务:` + prompt;
      }

      console.log(prompt);

      const result = await window.electronAPI.translateText({
        text,
        targetLanguage,
        prompt
      });

      if (!result) {
        throw new Error('翻译结果为空');
      }

      console.log('翻译成功:', {
        原文: text,
        译文: result,
        目标语言: targetLanguage
      });

      return result;

    } catch (error) {
      console.error('翻译服务错误:', {
        错误类型: error instanceof Error ? error.constructor.name : '未知错误类型',
        错误信息: error instanceof Error ? error.message : String(error),
        原始错误: error
      });

      // 处理特定的错误类型
      let errorMessage = '翻译失败';
      if (error instanceof Error) {
        if (error.message.includes('API 端点不存在')) {
          errorMessage = '翻译服务配置错误，请检查 API 地址';
        } else if (error.message.includes('API 认证失败')) {
          errorMessage = 'API 密钥无效，请检查配置';
        } else if (error.message.includes('调用频率超限')) {
          errorMessage = '翻译请求过于频繁，请稍后再试';
        } else {
          errorMessage = error.message;
        }
      }

      throw new Error(errorMessage);
    }
  });
}
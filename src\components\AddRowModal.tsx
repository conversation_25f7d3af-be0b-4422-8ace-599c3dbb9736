import React from 'react';
import { Modal, Form, Input } from 'antd';

interface AddRowModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: { key: string; chinese: string; module: string }) => void;
}

export const AddRowModal: React.FC<AddRowModalProps> = ({
  visible,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
      form.resetFields();
    } catch (error) {
      console.error('Validate Failed:', error);
    }
  };

  return (
    <Modal
      title="添加新行"
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="module"
          label="模块"
          rules={[{ required: true, message: '请输入模块名称' }]}
        >
          <Input placeholder="请输入模块名称" />
        </Form.Item>
        <Form.Item
          name="key"
          label="Key"
          rules={[{ required: true, message: '请输入 Key' }]}
        >
          <Input placeholder="请输入 Key" />
        </Form.Item>
        <Form.Item
          name="chinese"
          label="中文"
          rules={[{ required: true, message: '请输入中文' }]}
        >
          <Input.TextArea placeholder="请输入中文" rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
}; 
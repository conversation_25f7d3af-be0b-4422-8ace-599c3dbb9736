# Electron 构建配置优化

## 问题描述

原来的构建配置会生成完整的安装包（Windows 的 NSIS 安装程序、Mac 的 DMG 文件等），这导致构建过程非常缓慢，经常卡在打包阶段。

## 解决方案

修改 electron-builder 配置，只生成 unpacked 目录版本，大大加快构建速度。

## 配置变化

### 1. electron-builder.json 修改

#### Windows 配置
```json
// 修改前
"win": {
  "target": [
    {
      "target": "nsis",  // NSIS 安装程序
      "arch": ["x64"]
    }
  ]
}

// 修改后
"win": {
  "target": [
    {
      "target": "dir",   // 只生成目录
      "arch": ["x64"]
    }
  ]
}
```

#### Mac 配置
```json
// 修改前
"mac": {
  "target": ["dmg", "zip"]  // DMG 和 ZIP 文件
}

// 修改后
"mac": {
  "target": ["dir"]         // 只生成目录
}
```

#### 移除的配置
- 移除了 `nsis` 配置块，因为不再使用 NSIS 安装程序

### 2. package.json 脚本增强

添加了新的构建脚本：

```json
{
  "scripts": {
    "build": "rimraf dist dist-electron && vite build && electron-builder",
    "build:dir": "rimraf dist dist-electron && vite build && electron-builder --dir"
  }
}
```

## 使用方法

### 快速构建（推荐）
```bash
npm run build:dir
```
或者直接使用原来的命令（现在也只生成目录版本）：
```bash
npm run build
```

### 构建结果

构建完成后，应用程序将位于：
```
release/{version}/win-unpacked/
```

可以直接运行其中的 `.exe` 文件来启动应用。

## 优势

1. **构建速度快**：不需要生成安装包，节省大量时间
2. **文件体积小**：不包含安装程序的额外文件
3. **调试方便**：可以直接访问所有应用文件
4. **部署简单**：可以直接压缩整个目录进行分发

## 如果需要安装包

如果将来需要生成完整的安装包，可以：

1. **临时生成安装包**：
```bash
npx electron-builder --win nsis
```

2. **恢复原配置**：
修改 electron-builder.json 中的 target 配置：
```json
"win": {
  "target": [
    {
      "target": "nsis",
      "arch": ["x64"]
    }
  ]
}
```

## 注意事项

1. **分发方式**：现在需要手动压缩 `win-unpacked` 目录来分发应用
2. **自动更新**：如果使用自动更新功能，可能需要调整更新逻辑
3. **代码签名**：unpacked 版本仍然可以进行代码签名

## 测试建议

1. 运行 `npm run build:dir` 测试构建速度
2. 检查生成的 `release/{version}/win-unpacked/` 目录
3. 运行生成的 `.exe` 文件确保应用正常工作
4. 验证所有功能是否正常（翻译、导入导出等）

# 配置文件管理方案

## 问题背景

在开源项目中，我们需要：
1. 提供配置文件模板供用户参考
2. 保护敏感信息（API 密钥等）不被提交到 Git
3. 让用户能够方便地配置自己的参数

## 推荐方案：配置文件模板

### 文件结构
```
electron/
├── config.example.ts    # 配置模板（提交到 Git）
└── config.ts           # 实际配置（不提交到 Git）
```

### 使用流程

#### 1. 首次设置
```bash
# 克隆项目后
git clone <your-repo>
cd <your-project>

# 安装依赖（会自动创建配置文件）
npm install

# 或者手动创建配置文件
npm run setup
```

#### 2. 配置 API 参数
编辑 `electron/config.ts` 文件：
```typescript
const API_CONFIG = {
  baseURL: 'https://your-actual-api-endpoint.com',
  apiKey: 'your-actual-api-key',
  model: 'your-actual-model-name'
};
```

### 自动化脚本

#### setup-config.js
- 自动检查配置文件是否存在
- 如果不存在，从模板创建配置文件
- 提供友好的提示信息

#### package.json 脚本
```json
{
  "scripts": {
    "setup": "node scripts/setup-config.js",
    "postinstall": "node scripts/setup-config.js"
  }
}
```

### Git 配置

#### .gitignore
```
# Configuration files with sensitive data
electron/config.ts
```

#### 提交的文件
- ✅ `electron/config.example.ts` - 配置模板
- ✅ `scripts/setup-config.js` - 设置脚本
- ❌ `electron/config.ts` - 实际配置（被忽略）

## 备选方案：环境变量

### 文件结构
```
.env.example    # 环境变量模板（提交到 Git）
.env           # 实际环境变量（不提交到 Git）
```

### 使用方法
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件
API_BASE_URL=https://your-api-endpoint.com
API_KEY=your-api-key
API_MODEL=your-model-name
```

### 配置文件
```typescript
// electron/config.env.ts
const API_CONFIG = {
  baseURL: process.env.API_BASE_URL || 'default-url',
  apiKey: process.env.API_KEY || 'default-key',
  model: process.env.API_MODEL || 'default-model'
};
```

## 方案对比

| 特性 | 配置文件模板 | 环境变量 |
|------|-------------|----------|
| 易用性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 类型安全 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| IDE 支持 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 部署灵活性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 配置复杂度 | ⭐⭐⭐⭐ | ⭐⭐⭐ |

## 推荐使用配置文件模板方案

### 优势
1. **类型安全**：TypeScript 完整支持
2. **IDE 友好**：自动补全、语法检查
3. **易于维护**：配置集中在一个文件
4. **用户友好**：自动化设置脚本

### 最佳实践
1. 提供详细的配置说明文档
2. 使用自动化脚本简化设置过程
3. 在 README 中明确说明配置步骤
4. 提供配置验证功能（可选）

## 发布到 GitHub 的步骤

1. **准备配置文件**
```bash
# 确保配置模板存在
ls electron/config.example.ts

# 确保实际配置被忽略
git status  # 不应该看到 electron/config.ts
```

2. **提交代码**
```bash
git add .
git commit -m "feat: 添加配置文件管理和自动化设置"
git push origin main
```

3. **用户使用流程**
```bash
git clone <your-repo>
cd <your-project>
npm install  # 自动创建配置文件
# 编辑 electron/config.ts
npm run dev
```

这样既保护了你的敏感信息，又为用户提供了便捷的配置方式。

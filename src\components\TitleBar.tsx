import { useState, useEffect } from 'react';
import { MinusOutlined, BorderOutlined, CloseOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import './TitleBar.scss';

export const TitleBar: React.FC = () => {
  const [isMaximized, setIsMaximized] = useState(false);

  useEffect(() => {
    const handleMaximizedState = (maximized: boolean) => {
      setIsMaximized(maximized);
    };

    window.ipcRenderer.on('window-maximized-state', handleMaximizedState);

    return () => {
      window.ipcRenderer.off('window-maximized-state', handleMaximizedState);
    };
  }, []);

  const handleMinimize = () => {
    window.ipcRenderer.send('window-minimize');
  };

  const handleMaximize = () => {
    window.ipcRenderer.send('window-maximize');
  };

  const handleClose = () => {
    window.ipcRenderer.send('window-close');
  };

  return (
    <div className="title-bar">
      <div className="title">多语言管理系统</div>
      <div className="window-controls">
        <button onClick={handleMinimize} className="control-button minimize" title="最小化">
          <MinusOutlined />
        </button>
        <button onClick={handleMaximize} className="control-button maximize" title={isMaximized ? "还原" : "最大化"}>
          {isMaximized ? <FullscreenExitOutlined /> : <BorderOutlined />}
        </button>
        <button onClick={handleClose} className="control-button close" title="关闭">
          <CloseOutlined />
        </button>
      </div>
    </div>
  );
}; 
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { MinusOutlined, BorderOutlined, CloseOutlined } from '@ant-design/icons';
import type { Ip<PERSON><PERSON><PERSON><PERSON> } from 'electron';

// 定义一个统一的API对象
const electronAPI = {
  // 文件对话框相关
  showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
  saveExcelFile: (params: any) => ipcRenderer.invoke('save-excel-file', params),
  
  // 翻译相关
  translateText: (params: any) => ipcRenderer.invoke('translate-text', params),
  
  // 窗口控制
  windowControl: {
    minimize: () => ipcRenderer.send('window-minimize'),
    maximize: () => ipcRenderer.send('window-maximize'),
    close: () => ipcRenderer.send('window-close')
  },
  
  // 事件监听
  on: (channel: string, callback: Function) => {
    const validChannels = ['window-maximized-state', 'save-dialog-selected'];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (event, ...args) => callback(...args));
    }
  },
  
  // 移除事件监听
  off: (channel: string, callback: Function) => {
    const validChannels = ['window-maximized-state', 'save-dialog-selected'];
    if (validChannels.includes(channel)) {
      ipcRenderer.removeListener(channel, callback);
    }
  }
};

// 使用 contextBridge 暴露 API
try {
  contextBridge.exposeInMainWorld('electron', electronAPI);
} catch (error) {
  console.error('Failed to expose electron API:', error);
}

// 暴露 ipcRenderer 到 window 对象
contextBridge.exposeInMainWorld('ipcRenderer', {
  send: (channel: string, ...args: any[]) => {
    ipcRenderer.send(channel, ...args)
  },
  on: (channel: string, func: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (event, ...args) => func(...args))
  },
  off: (channel: string, func: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, func)
  },
  once: (channel: string, func: (...args: any[]) => void) => {
    ipcRenderer.once(channel, (event, ...args) => func(...args))
  },
  invoke: (channel: string, ...args: any[]) => {
    ipcRenderer.invoke(channel, ...args)
  }
})

// 类型声明
declare global {
  interface Window {
    electronAPI: {
      translateText: (params: {
        text: string;
        targetLanguage: string;
        prompt: string;
      }) => Promise<string>;
      // ... 其他 API 类型
    }
    ipcRenderer: IpcRenderer;
  }
}

export {}

// --------- Preload scripts loading ---------
function domReady(condition = ['complete', 'interactive']) {
  return new Promise(resolve => {
    if (condition.includes(document.readyState)) {
      resolve(true)
    } else {
      document.addEventListener('readystatechange', () => {
        if (condition.includes(document.readyState)) {
          resolve(true)
        }
      })
    }
  })
}

const safeDOM = {
  append(parent, child) {
    if (!Array.from(parent.children).find(e => e === child)) {
      return parent.appendChild(child)
    }
  },
  remove(parent, child) {
    if (Array.from(parent.children).find(e => e === child)) {
      return parent.removeChild(child)
    }
  },
}

/**
 * https://tobiasahlin.com/spinkit
 * https://connoratherton.com/loaders
 * https://projects.lukehaas.me/css-loaders
 * https://matejkustec.github.io/SpinThatShit
 */
function useLoading() {
  const className = `loaders-css__square-spin`
  const styleContent = `
    @keyframes square-spin {
      25% { transform: perspective(100px) rotateX(180deg) rotateY(0); }
      50% { transform: perspective(100px) rotateX(180deg) rotateY(180deg); }
      75% { transform: perspective(100px) rotateX(0) rotateY(180deg); }
      100% { transform: perspective(100px) rotateX(0) rotateY(0); }
    }
    .${className} > div {
      animation-fill-mode: both;
      width: 50px;
      height: 50px;
      background: #fff;
      animation: square-spin 3s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
    }
    .app-loading-wrap {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #282c34;
      z-index: 9;
    }
  `
  const oStyle = document.createElement('style')
  const oDiv = document.createElement('div')

  oStyle.id = 'app-loading-style'
  oStyle.innerHTML = styleContent
  oDiv.className = 'app-loading-wrap'
  oDiv.innerHTML = `<div class="${className}"><div></div></div>`

  return {
    appendLoading() {
      safeDOM.append(document.head, oStyle)
      safeDOM.append(document.body, oDiv)
    },
    removeLoading() {
      safeDOM.remove(document.head, oStyle)
      safeDOM.remove(document.body, oDiv)
    },
  }
}

// ----------------------------------------------------------------------

const { appendLoading, removeLoading } = useLoading()
domReady().then(appendLoading)

window.onmessage = ev => {
  ev.data.payload === 'removeLoading' && removeLoading()
}

setTimeout(removeLoading, 4999)

interface SaveDialogOptions {
  defaultPath: string;
}

interface SaveExcelParams {
  buffer: Uint8Array;
  filePath: string;
}

interface DOMElement {
  appendChild(child: DOMElement): void;
  removeChild(child: DOMElement): void;
}

function domHardReload() {
  // 实现代码...
}

// 使用 contextBridge 暴露 API
contextBridge.exposeInMainWorld('electronAPI', {
  translateText: (params: { text: string; targetLanguage: string; prompt: string }) => {
    return ipcRenderer.invoke('translate-text', params);
  }
});

contextBridge.exposeInMainWorld('ipcRenderer', {
  send: (channel: string, ...args: any[]) => ipcRenderer.send(channel, ...args),
  on: (channel: string, func: (...args: any[]) => void) => ipcRenderer.on(channel, func),
  off: (channel: string, func: (...args: any[]) => void) => ipcRenderer.off(channel, func),
  once: (channel: string, func: (...args: any[]) => void) => ipcRenderer.once(channel, func),
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args)
});
.title-bar {
  -webkit-app-region: drag;
  height: 32px;
  background: #1e1e1e;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  color: #ffffff;
  user-select: none;
  border-bottom: 1px solid #333333;

  .title {
    font-size: 13px;
    font-weight: 400;
    opacity: 0.9;
  }

  .window-controls {
    -webkit-app-region: no-drag;
    display: flex;
    height: 100%;

    .control-button {
      background: transparent;
      border: none;
      color: #ffffff;
      width: 46px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      opacity: 0.8;

      &:hover {
        opacity: 1;
        background-color: rgba(255, 255, 255, 0.1);
      }

      &:active {
        opacity: 0.6;
      }

      &.close {
        &:hover {
          background-color: #e81123;
        }
      }

      .anticon {
        font-size: 12px;
      }
    }
  }
} 